a:14:{s:6:"config";s:4971:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:10:"2.0.52-dev";s:11:"application";a:8:{s:3:"yii";s:10:"2.0.52-dev";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:2:"uz";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:16:{s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/base";s:67:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:62:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:60:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-grid/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:77:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-jui/src";}}s:33:"2amigos/yii2-arrayquery-component";a:3:{s:4:"name";s:33:"2amigos/yii2-arrayquery-component";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@dosamigos/arrayquery";s:75:"D:\OSPanel\domains\silverzavod\vendor/2amigos/yii2-arrayquery-component/src";}}s:17:"yii2mod/yii2-rbac";a:3:{s:4:"name";s:17:"yii2mod/yii2-rbac";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii2mod/rbac";s:55:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-rbac";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.25.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:10:"@yii/faker";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.6.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:23:"3.0.9999999.9999999-dev";s:5:"alias";a:10:{s:10:"@yii/queue";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:71:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:72:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/file";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:76:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:78:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:81:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:68:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.11.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap4/src";}}s:20:"yii2mod/yii2-swagger";a:3:{s:4:"name";s:20:"yii2mod/yii2-swagger";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:16:"@yii2mod/swagger";s:58:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-swagger";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:16149:"a:1:{s:8:"messages";a:25:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.422815;i:4;a:0:{}i:5;i:2611832;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.424007;i:4;a:0:{}i:5;i:2728840;}i:2;a:6:{i:0;s:53:"Bootstrap with app\components\SessionTimeoutComponent";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.424535;i:4;a:0:{}i:5;i:2770048;}i:3;a:6:{i:0;s:22:"Bootstrap with Closure";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.424541;i:4;a:0:{}i:5;i:2770424;}i:4;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.449039;i:4;a:0:{}i:5;i:3915624;}i:5;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.463717;i:4;a:0:{}i:5;i:4725880;}i:6;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.472511;i:4;a:0:{}i:5;i:5115544;}i:7;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.479644;i:4;a:0:{}i:5;i:5579952;}i:8;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.480536;i:4;a:0:{}i:5;i:5607344;}i:20;a:6:{i:0;s:57:"Route requested: 'api/manufacter/send-to-material-defect'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.484065;i:4;a:0:{}i:5;i:5780344;}i:21;a:6:{i:0;s:19:"Loading module: api";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.484083;i:4;a:0:{}i:5;i:5781992;}i:22;a:6:{i:0;s:52:"Route to run: api/manufacter/send-to-material-defect";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.4908;i:4;a:0:{}i:5;i:6128520;}i:23;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.51618;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7469416;}i:26;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.592216;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8000816;}i:29;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.62432;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8046728;}i:32;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.631439;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8342536;}i:35;a:6:{i:0;s:55:"User '7' logged in from 127.0.0.1. Session not enabled.";i:1;i:4;i:2;s:19:"yii\web\User::login";i:3;d:**********.64031;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8629184;}i:36;a:6:{i:0;s:65:"Rate limit skipped: "user" does not implement RateLimitInterface.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:**********.640363;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8629776;}i:37;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.642878;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8817360;}i:40;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.646574;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8823984;}i:43;a:6:{i:0;s:27:"Checking role: manufacturer";i:1;i:8;i:2;s:40:"yii\rbac\DbManager::checkAccessRecursive";i:3;d:**********.649821;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8827376;}i:44;a:6:{i:0;s:94:"Running action: app\modules\api\controllers\ManufacterController::actionSendToMaterialDefect()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.649854;i:4;a:0:{}i:5;i:8826536;}i:45;a:6:{i:0;s:795:"
            SELECT
                m.id as material_id,
                m.name as material_name,
                to_char(SUM(mp.quantity), 'FM999999999.########') as quantity,
                m.unit_type,
                CASE
            WHEN m.unit_type = 1 THEN 'Дона'
            WHEN m.unit_type = 2 THEN 'Килограмм'
            WHEN m.unit_type = 3 THEN 'Литр'
            ELSE 'Номаълум'
        END as unit_type_name
            FROM material_production mp
            JOIN material m ON m.id = mp.material_id
            WHERE mp.deleted_at IS NULL
            AND mp.quantity > 0
            AND mp.created_at >= '2025-06-03 00:00:00' AND mp.created_at < '2025-06-03 23:59:59'
            GROUP BY m.id, m.name, m.unit_type
            ORDER BY m.name
        ";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.652564;i:4;a:3:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:198;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:53;s:8:"function";s:12:"executeQuery";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:23;s:8:"function";s:21:"getAvailableMaterials";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}}i:5;i:8991952;}i:48;a:6:{i:0;s:1387:"
            SELECT
                md.id as defect_id,
                m.name as material_name,
                CASE
                    WHEN md.quantity = floor(md.quantity) THEN to_char(md.quantity, 'FM999999999')
                    ELSE to_char(md.quantity, 'FM999999999.99999999')
                END as quantity,
                md.description,
                md.created_at,
                m.unit_type,
                CASE
            WHEN m.unit_type = 1 THEN 'Дона'
            WHEN m.unit_type = 2 THEN 'Килограмм'
            WHEN m.unit_type = 3 THEN 'Литр'
            ELSE 'Номаълум'
        END as unit_type_name,
                CASE
                    WHEN md.accepted_user_id IS NOT NULL THEN true
                    ELSE false
                END as is_accepted,
                md.is_processed,
                u.full_name as added_by,
                au.full_name as accepted_by
            FROM material_defect md
            JOIN material m ON m.id = md.material_id
            LEFT JOIN users u ON u.id = md.add_user_id
            LEFT JOIN users au ON au.id = md.accepted_user_id
            WHERE md.deleted_at IS NULL 
            AND md.source = 2
            AND md.add_user_id = 7
            AND md.created_at >= '2025-06-03 00:00:00' AND md.created_at < '2025-06-03 23:59:59'
            ORDER BY md.created_at DESC
        ";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.661299;i:4;a:3:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:102;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:24;s:8:"function";s:18:"getDefectMaterials";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:411;s:8:"function";s:21:"getMaterialDefectData";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}}i:5;i:9031632;}i:51;a:6:{i:0;s:1365:"
            SELECT
                msg.id as group_id,
                msg.created_at,
                msg.accepted_at,
                CASE
                    WHEN msg.accepted_user_id IS NOT NULL THEN true
                    ELSE false
                END as is_accepted,
                u.full_name as added_by,
                au.full_name as accepted_by,
                ms.material_id,
                m.name as material_name,
                to_char(ms.quantity, 'FM999999999.########') as quantity,
                m.unit_type,
                CASE
            WHEN m.unit_type = 1 THEN 'Дона'
            WHEN m.unit_type = 2 THEN 'Килограмм'
            WHEN m.unit_type = 3 THEN 'Литр'
            ELSE 'Номаълум'
        END as unit_type_name
            FROM material_status_group msg
            LEFT JOIN users u ON u.id = msg.add_user_id
            LEFT JOIN users au ON au.id = msg.accepted_user_id
            LEFT JOIN material_status ms ON ms.status_group_id = msg.id AND ms.deleted_at IS NULL
            LEFT JOIN material m ON m.id = ms.material_id
            WHERE msg.deleted_at IS NULL
            AND msg.status = 5
            AND msg.add_user_id = 7
            AND msg.created_at >= '2025-06-03 00:00:00' AND msg.created_at < '2025-06-03 23:59:59'
            ORDER BY msg.created_at DESC, m.name
        ";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.66636;i:4;a:3:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:148;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:25;s:8:"function";s:18:"getReturnMaterials";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:411;s:8:"function";s:21:"getMaterialDefectData";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}}i:5;i:9040256;}}}";s:9:"profiling";s:25856:"a:3:{s:6:"memory";i:9277544;s:4:"time";d:0.26956605911254883;s:8:"messages";a:18:{i:24;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.516268;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7470920;}i:25;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.590189;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7473224;}i:27;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.592294;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8002768;}i:28;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.622987;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8018504;}i:30;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.624354;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8048592;}i:31;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.627659;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8050528;}i:33;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.631473;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8346800;}i:34;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.636156;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8349560;}i:38;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.642915;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8819968;}i:39;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.64596;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8822136;}i:41;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.646604;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8826592;}i:42;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.649201;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8828696;}i:46;a:6:{i:0;s:795:"
            SELECT
                m.id as material_id,
                m.name as material_name,
                to_char(SUM(mp.quantity), 'FM999999999.########') as quantity,
                m.unit_type,
                CASE
            WHEN m.unit_type = 1 THEN 'Дона'
            WHEN m.unit_type = 2 THEN 'Килограмм'
            WHEN m.unit_type = 3 THEN 'Литр'
            ELSE 'Номаълум'
        END as unit_type_name
            FROM material_production mp
            JOIN material m ON m.id = mp.material_id
            WHERE mp.deleted_at IS NULL
            AND mp.quantity > 0
            AND mp.created_at >= '2025-06-03 00:00:00' AND mp.created_at < '2025-06-03 23:59:59'
            GROUP BY m.id, m.name, m.unit_type
            ORDER BY m.name
        ";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.652605;i:4;a:3:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:198;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:53;s:8:"function";s:12:"executeQuery";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:23;s:8:"function";s:21:"getAvailableMaterials";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}}i:5;i:8996184;}i:47;a:6:{i:0;s:795:"
            SELECT
                m.id as material_id,
                m.name as material_name,
                to_char(SUM(mp.quantity), 'FM999999999.########') as quantity,
                m.unit_type,
                CASE
            WHEN m.unit_type = 1 THEN 'Дона'
            WHEN m.unit_type = 2 THEN 'Килограмм'
            WHEN m.unit_type = 3 THEN 'Литр'
            ELSE 'Номаълум'
        END as unit_type_name
            FROM material_production mp
            JOIN material m ON m.id = mp.material_id
            WHERE mp.deleted_at IS NULL
            AND mp.quantity > 0
            AND mp.created_at >= '2025-06-03 00:00:00' AND mp.created_at < '2025-06-03 23:59:59'
            GROUP BY m.id, m.name, m.unit_type
            ORDER BY m.name
        ";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.660213;i:4;a:3:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:198;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:53;s:8:"function";s:12:"executeQuery";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:23;s:8:"function";s:21:"getAvailableMaterials";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}}i:5;i:8999768;}i:49;a:6:{i:0;s:1387:"
            SELECT
                md.id as defect_id,
                m.name as material_name,
                CASE
                    WHEN md.quantity = floor(md.quantity) THEN to_char(md.quantity, 'FM999999999')
                    ELSE to_char(md.quantity, 'FM999999999.99999999')
                END as quantity,
                md.description,
                md.created_at,
                m.unit_type,
                CASE
            WHEN m.unit_type = 1 THEN 'Дона'
            WHEN m.unit_type = 2 THEN 'Килограмм'
            WHEN m.unit_type = 3 THEN 'Литр'
            ELSE 'Номаълум'
        END as unit_type_name,
                CASE
                    WHEN md.accepted_user_id IS NOT NULL THEN true
                    ELSE false
                END as is_accepted,
                md.is_processed,
                u.full_name as added_by,
                au.full_name as accepted_by
            FROM material_defect md
            JOIN material m ON m.id = md.material_id
            LEFT JOIN users u ON u.id = md.add_user_id
            LEFT JOIN users au ON au.id = md.accepted_user_id
            WHERE md.deleted_at IS NULL 
            AND md.source = 2
            AND md.add_user_id = 7
            AND md.created_at >= '2025-06-03 00:00:00' AND md.created_at < '2025-06-03 23:59:59'
            ORDER BY md.created_at DESC
        ";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.66135;i:4;a:3:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:102;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:24;s:8:"function";s:18:"getDefectMaterials";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:411;s:8:"function";s:21:"getMaterialDefectData";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}}i:5;i:9036568;}i:50;a:6:{i:0;s:1387:"
            SELECT
                md.id as defect_id,
                m.name as material_name,
                CASE
                    WHEN md.quantity = floor(md.quantity) THEN to_char(md.quantity, 'FM999999999')
                    ELSE to_char(md.quantity, 'FM999999999.99999999')
                END as quantity,
                md.description,
                md.created_at,
                m.unit_type,
                CASE
            WHEN m.unit_type = 1 THEN 'Дона'
            WHEN m.unit_type = 2 THEN 'Килограмм'
            WHEN m.unit_type = 3 THEN 'Литр'
            ELSE 'Номаълум'
        END as unit_type_name,
                CASE
                    WHEN md.accepted_user_id IS NOT NULL THEN true
                    ELSE false
                END as is_accepted,
                md.is_processed,
                u.full_name as added_by,
                au.full_name as accepted_by
            FROM material_defect md
            JOIN material m ON m.id = md.material_id
            LEFT JOIN users u ON u.id = md.add_user_id
            LEFT JOIN users au ON au.id = md.accepted_user_id
            WHERE md.deleted_at IS NULL 
            AND md.source = 2
            AND md.add_user_id = 7
            AND md.created_at >= '2025-06-03 00:00:00' AND md.created_at < '2025-06-03 23:59:59'
            ORDER BY md.created_at DESC
        ";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.665741;i:4;a:3:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:102;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:24;s:8:"function";s:18:"getDefectMaterials";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:411;s:8:"function";s:21:"getMaterialDefectData";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}}i:5;i:9040800;}i:52;a:6:{i:0;s:1365:"
            SELECT
                msg.id as group_id,
                msg.created_at,
                msg.accepted_at,
                CASE
                    WHEN msg.accepted_user_id IS NOT NULL THEN true
                    ELSE false
                END as is_accepted,
                u.full_name as added_by,
                au.full_name as accepted_by,
                ms.material_id,
                m.name as material_name,
                to_char(ms.quantity, 'FM999999999.########') as quantity,
                m.unit_type,
                CASE
            WHEN m.unit_type = 1 THEN 'Дона'
            WHEN m.unit_type = 2 THEN 'Килограмм'
            WHEN m.unit_type = 3 THEN 'Литр'
            ELSE 'Номаълум'
        END as unit_type_name
            FROM material_status_group msg
            LEFT JOIN users u ON u.id = msg.add_user_id
            LEFT JOIN users au ON au.id = msg.accepted_user_id
            LEFT JOIN material_status ms ON ms.status_group_id = msg.id AND ms.deleted_at IS NULL
            LEFT JOIN material m ON m.id = ms.material_id
            WHERE msg.deleted_at IS NULL
            AND msg.status = 5
            AND msg.add_user_id = 7
            AND msg.created_at >= '2025-06-03 00:00:00' AND msg.created_at < '2025-06-03 23:59:59'
            ORDER BY msg.created_at DESC, m.name
        ";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.666431;i:4;a:3:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:148;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:25;s:8:"function";s:18:"getReturnMaterials";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:411;s:8:"function";s:21:"getMaterialDefectData";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}}i:5;i:9045168;}i:53;a:6:{i:0;s:1365:"
            SELECT
                msg.id as group_id,
                msg.created_at,
                msg.accepted_at,
                CASE
                    WHEN msg.accepted_user_id IS NOT NULL THEN true
                    ELSE false
                END as is_accepted,
                u.full_name as added_by,
                au.full_name as accepted_by,
                ms.material_id,
                m.name as material_name,
                to_char(ms.quantity, 'FM999999999.########') as quantity,
                m.unit_type,
                CASE
            WHEN m.unit_type = 1 THEN 'Дона'
            WHEN m.unit_type = 2 THEN 'Килограмм'
            WHEN m.unit_type = 3 THEN 'Литр'
            ELSE 'Номаълум'
        END as unit_type_name
            FROM material_status_group msg
            LEFT JOIN users u ON u.id = msg.add_user_id
            LEFT JOIN users au ON au.id = msg.accepted_user_id
            LEFT JOIN material_status ms ON ms.status_group_id = msg.id AND ms.deleted_at IS NULL
            LEFT JOIN material m ON m.id = ms.material_id
            WHERE msg.deleted_at IS NULL
            AND msg.status = 5
            AND msg.add_user_id = 7
            AND msg.created_at >= '2025-06-03 00:00:00' AND msg.created_at < '2025-06-03 23:59:59'
            ORDER BY msg.created_at DESC, m.name
        ";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.675274;i:4;a:3:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:148;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:25;s:8:"function";s:18:"getReturnMaterials";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:411;s:8:"function";s:21:"getMaterialDefectData";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}}i:5;i:9048032;}}}";s:2:"db";s:24610:"a:1:{s:8:"messages";a:16:{i:27;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.592294;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8002768;}i:28;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.622987;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8018504;}i:30;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.624354;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8048592;}i:31;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.627659;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8050528;}i:33;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.631473;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8346800;}i:34;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.636156;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8349560;}i:38;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.642915;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8819968;}i:39;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.64596;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8822136;}i:41;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.646604;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8826592;}i:42;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.649201;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8828696;}i:46;a:6:{i:0;s:795:"
            SELECT
                m.id as material_id,
                m.name as material_name,
                to_char(SUM(mp.quantity), 'FM999999999.########') as quantity,
                m.unit_type,
                CASE
            WHEN m.unit_type = 1 THEN 'Дона'
            WHEN m.unit_type = 2 THEN 'Килограмм'
            WHEN m.unit_type = 3 THEN 'Литр'
            ELSE 'Номаълум'
        END as unit_type_name
            FROM material_production mp
            JOIN material m ON m.id = mp.material_id
            WHERE mp.deleted_at IS NULL
            AND mp.quantity > 0
            AND mp.created_at >= '2025-06-03 00:00:00' AND mp.created_at < '2025-06-03 23:59:59'
            GROUP BY m.id, m.name, m.unit_type
            ORDER BY m.name
        ";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.652605;i:4;a:3:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:198;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:53;s:8:"function";s:12:"executeQuery";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:23;s:8:"function";s:21:"getAvailableMaterials";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}}i:5;i:8996184;}i:47;a:6:{i:0;s:795:"
            SELECT
                m.id as material_id,
                m.name as material_name,
                to_char(SUM(mp.quantity), 'FM999999999.########') as quantity,
                m.unit_type,
                CASE
            WHEN m.unit_type = 1 THEN 'Дона'
            WHEN m.unit_type = 2 THEN 'Килограмм'
            WHEN m.unit_type = 3 THEN 'Литр'
            ELSE 'Номаълум'
        END as unit_type_name
            FROM material_production mp
            JOIN material m ON m.id = mp.material_id
            WHERE mp.deleted_at IS NULL
            AND mp.quantity > 0
            AND mp.created_at >= '2025-06-03 00:00:00' AND mp.created_at < '2025-06-03 23:59:59'
            GROUP BY m.id, m.name, m.unit_type
            ORDER BY m.name
        ";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.660213;i:4;a:3:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:198;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:53;s:8:"function";s:12:"executeQuery";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:23;s:8:"function";s:21:"getAvailableMaterials";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}}i:5;i:8999768;}i:49;a:6:{i:0;s:1387:"
            SELECT
                md.id as defect_id,
                m.name as material_name,
                CASE
                    WHEN md.quantity = floor(md.quantity) THEN to_char(md.quantity, 'FM999999999')
                    ELSE to_char(md.quantity, 'FM999999999.99999999')
                END as quantity,
                md.description,
                md.created_at,
                m.unit_type,
                CASE
            WHEN m.unit_type = 1 THEN 'Дона'
            WHEN m.unit_type = 2 THEN 'Килограмм'
            WHEN m.unit_type = 3 THEN 'Литр'
            ELSE 'Номаълум'
        END as unit_type_name,
                CASE
                    WHEN md.accepted_user_id IS NOT NULL THEN true
                    ELSE false
                END as is_accepted,
                md.is_processed,
                u.full_name as added_by,
                au.full_name as accepted_by
            FROM material_defect md
            JOIN material m ON m.id = md.material_id
            LEFT JOIN users u ON u.id = md.add_user_id
            LEFT JOIN users au ON au.id = md.accepted_user_id
            WHERE md.deleted_at IS NULL 
            AND md.source = 2
            AND md.add_user_id = 7
            AND md.created_at >= '2025-06-03 00:00:00' AND md.created_at < '2025-06-03 23:59:59'
            ORDER BY md.created_at DESC
        ";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.66135;i:4;a:3:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:102;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:24;s:8:"function";s:18:"getDefectMaterials";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:411;s:8:"function";s:21:"getMaterialDefectData";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}}i:5;i:9036568;}i:50;a:6:{i:0;s:1387:"
            SELECT
                md.id as defect_id,
                m.name as material_name,
                CASE
                    WHEN md.quantity = floor(md.quantity) THEN to_char(md.quantity, 'FM999999999')
                    ELSE to_char(md.quantity, 'FM999999999.99999999')
                END as quantity,
                md.description,
                md.created_at,
                m.unit_type,
                CASE
            WHEN m.unit_type = 1 THEN 'Дона'
            WHEN m.unit_type = 2 THEN 'Килограмм'
            WHEN m.unit_type = 3 THEN 'Литр'
            ELSE 'Номаълум'
        END as unit_type_name,
                CASE
                    WHEN md.accepted_user_id IS NOT NULL THEN true
                    ELSE false
                END as is_accepted,
                md.is_processed,
                u.full_name as added_by,
                au.full_name as accepted_by
            FROM material_defect md
            JOIN material m ON m.id = md.material_id
            LEFT JOIN users u ON u.id = md.add_user_id
            LEFT JOIN users au ON au.id = md.accepted_user_id
            WHERE md.deleted_at IS NULL 
            AND md.source = 2
            AND md.add_user_id = 7
            AND md.created_at >= '2025-06-03 00:00:00' AND md.created_at < '2025-06-03 23:59:59'
            ORDER BY md.created_at DESC
        ";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.665741;i:4;a:3:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:102;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:24;s:8:"function";s:18:"getDefectMaterials";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:411;s:8:"function";s:21:"getMaterialDefectData";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}}i:5;i:9040800;}i:52;a:6:{i:0;s:1365:"
            SELECT
                msg.id as group_id,
                msg.created_at,
                msg.accepted_at,
                CASE
                    WHEN msg.accepted_user_id IS NOT NULL THEN true
                    ELSE false
                END as is_accepted,
                u.full_name as added_by,
                au.full_name as accepted_by,
                ms.material_id,
                m.name as material_name,
                to_char(ms.quantity, 'FM999999999.########') as quantity,
                m.unit_type,
                CASE
            WHEN m.unit_type = 1 THEN 'Дона'
            WHEN m.unit_type = 2 THEN 'Килограмм'
            WHEN m.unit_type = 3 THEN 'Литр'
            ELSE 'Номаълум'
        END as unit_type_name
            FROM material_status_group msg
            LEFT JOIN users u ON u.id = msg.add_user_id
            LEFT JOIN users au ON au.id = msg.accepted_user_id
            LEFT JOIN material_status ms ON ms.status_group_id = msg.id AND ms.deleted_at IS NULL
            LEFT JOIN material m ON m.id = ms.material_id
            WHERE msg.deleted_at IS NULL
            AND msg.status = 5
            AND msg.add_user_id = 7
            AND msg.created_at >= '2025-06-03 00:00:00' AND msg.created_at < '2025-06-03 23:59:59'
            ORDER BY msg.created_at DESC, m.name
        ";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.666431;i:4;a:3:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:148;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:25;s:8:"function";s:18:"getReturnMaterials";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:411;s:8:"function";s:21:"getMaterialDefectData";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}}i:5;i:9045168;}i:53;a:6:{i:0;s:1365:"
            SELECT
                msg.id as group_id,
                msg.created_at,
                msg.accepted_at,
                CASE
                    WHEN msg.accepted_user_id IS NOT NULL THEN true
                    ELSE false
                END as is_accepted,
                u.full_name as added_by,
                au.full_name as accepted_by,
                ms.material_id,
                m.name as material_name,
                to_char(ms.quantity, 'FM999999999.########') as quantity,
                m.unit_type,
                CASE
            WHEN m.unit_type = 1 THEN 'Дона'
            WHEN m.unit_type = 2 THEN 'Килограмм'
            WHEN m.unit_type = 3 THEN 'Литр'
            ELSE 'Номаълум'
        END as unit_type_name
            FROM material_status_group msg
            LEFT JOIN users u ON u.id = msg.add_user_id
            LEFT JOIN users au ON au.id = msg.accepted_user_id
            LEFT JOIN material_status ms ON ms.status_group_id = msg.id AND ms.deleted_at IS NULL
            LEFT JOIN material m ON m.id = ms.material_id
            WHERE msg.deleted_at IS NULL
            AND msg.status = 5
            AND msg.add_user_id = 7
            AND msg.created_at >= '2025-06-03 00:00:00' AND msg.created_at < '2025-06-03 23:59:59'
            ORDER BY msg.created_at DESC, m.name
        ";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.675274;i:4;a:3:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:148;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\MaterialDefectDataService.php";s:4:"line";i:25;s:8:"function";s:18:"getReturnMaterials";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:411;s:8:"function";s:21:"getMaterialDefectData";s:5:"class";s:61:"app\modules\api\services\manufacter\MaterialDefectDataService";s:4:"type";s:2:"->";}}i:5;i:9048032;}}}";s:5:"event";s:4496:"a:24:{i:0;a:5:{s:4:"time";d:**********.483082;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:**********.491046;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:**********.491099;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\api\ApiModule";}i:3;a:5:{s:4:"time";d:**********.507445;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:4;a:5:{s:4:"time";d:**********.590169;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:5;a:5:{s:4:"time";d:**********.636476;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\modules\api\models\Users";}i:6;a:5:{s:4:"time";d:**********.636541;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\modules\api\models\Users";}i:7;a:5:{s:4:"time";d:**********.636841;s:4:"name";s:11:"beforeLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:8;a:5:{s:4:"time";d:**********.640127;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:9;a:5:{s:4:"time";d:**********.640154;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:10;a:5:{s:4:"time";d:**********.640183;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:11;a:5:{s:4:"time";d:**********.640193;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:12;a:5:{s:4:"time";d:**********.640201;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:13;a:5:{s:4:"time";d:**********.640208;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:14;a:5:{s:4:"time";d:**********.640216;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:15;a:5:{s:4:"time";d:**********.640338;s:4:"name";s:10:"afterLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:16;a:5:{s:4:"time";d:**********.640399;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:48:"app\modules\api\controllers\ManufacterController";}i:17;a:5:{s:4:"time";d:**********.6764;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:48:"app\modules\api\controllers\ManufacterController";}i:18;a:5:{s:4:"time";d:**********.677119;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\api\ApiModule";}i:19;a:5:{s:4:"time";d:**********.677131;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:20;a:5:{s:4:"time";d:**********.677146;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:21;a:5:{s:4:"time";d:**********.677156;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:22;a:5:{s:4:"time";d:**********.679169;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:23;a:5:{s:4:"time";d:**********.679333;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:**********.412621;s:3:"end";d:**********.682426;s:6:"memory";i:9277544;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:2246:"a:3:{s:8:"messages";a:11:{i:9;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.483754;i:4;a:0:{}i:5;i:5772112;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.483776;i:4;a:0:{}i:5;i:5772864;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.483785;i:4;a:0:{}i:5;i:5773616;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.483792;i:4;a:0:{}i:5;i:5774368;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.483798;i:4;a:0:{}i:5;i:5775120;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.483805;i:4;a:0:{}i:5;i:5775872;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:5:"login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.483811;i:4;a:0:{}i:5;i:5776624;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:6:"logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.483817;i:4;a:0:{}i:5;i:5777376;}i:17;a:6:{i:0;a:3:{s:4:"rule";s:13:"site/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.483823;i:4;a:0:{}i:5;i:5778768;}i:18;a:6:{i:0;s:69:"Request parsed with URL rule: api/<controller:[\w-]+>/<action:[\w-]+>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:**********.484034;i:4;a:0:{}i:5;i:5780864;}i:19;a:6:{i:0;a:3:{s:4:"rule";s:39:"api/<controller:[\w-]+>/<action:[\w-]+>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.484051;i:4;a:0:{}i:5;i:5780680;}}s:5:"route";s:38:"api/manufacter/send-to-material-defect";s:6:"action";s:78:"app\modules\api\controllers\ManufacterController::actionSendToMaterialDefect()";}";s:7:"request";s:4198:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:9:{s:13:"authorization";s:47:"Bearer 681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy";s:10:"user-agent";s:21:"PostmanRuntime/7.44.0";s:6:"accept";s:3:"*/*";s:13:"cache-control";s:8:"no-cache";s:13:"postman-token";s:36:"90ac8b7f-50b7-4d76-810d-f4f37c1ee87f";s:4:"host";s:6:"silver";s:15:"accept-encoding";s:17:"gzip, deflate, br";s:10:"connection";s:10:"keep-alive";s:6:"cookie";s:216:"PHPSESSID=5kmna394fkk624c3mdo5a4tcspgksban; _csrf=122dfc0a9fdf737eed675d94f36c22b75683d1d1010d527a00b1a971721d03fda%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22mQAy1MIxX8gNk5d7r1CbyrNJqKwlDd6G%22%3B%7D";}s:15:"responseHeaders";a:9:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:4:"Vary";s:6:"Accept";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"683ea237739f8";s:16:"X-Debug-Duration";s:3:"268";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=683ea237739f8";s:10:"Set-Cookie";s:204:"_csrf=f80c194cbecd84f233f3d0a6fcfddfc03c3788c7a78bb59538e6296577397c5ea%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22diw-MhDAGWSMsAfR80E2T0RIjZGPC6wp%22%3B%7D; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:38:"api/manufacter/send-to-material-defect";s:6:"action";s:78:"app\modules\api\controllers\ManufacterController::actionSendToMaterialDefect()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:38:{s:15:"REDIRECT_STATUS";s:3:"200";s:15:"HTTP_USER_AGENT";s:21:"PostmanRuntime/7.44.0";s:11:"HTTP_ACCEPT";s:3:"*/*";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:18:"HTTP_POSTMAN_TOKEN";s:36:"90ac8b7f-50b7-4d76-810d-f4f37c1ee87f";s:9:"HTTP_HOST";s:6:"silver";s:20:"HTTP_ACCEPT_ENCODING";s:17:"gzip, deflate, br";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:11:"HTTP_COOKIE";s:216:"PHPSESSID=5kmna394fkk624c3mdo5a4tcspgksban; _csrf=122dfc0a9fdf737eed675d94f36c22b75683d1d1010d527a00b1a971721d03fda%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22mQAy1MIxX8gNk5d7r1CbyrNJqKwlDd6G%22%3B%7D";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:6:"silver";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:44:"D:/OSPanel/domains/silverzavod/web/index.php";s:11:"REMOTE_PORT";s:5:"50457";s:12:"REDIRECT_URL";s:39:"/api/manufacter/send-to-material-defect";s:21:"REDIRECT_QUERY_STRING";s:15:"date=2025-06-03";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:3:"GET";s:12:"QUERY_STRING";s:15:"date=2025-06-03";s:11:"REQUEST_URI";s:55:"/api/manufacter/send-to-material-defect?date=2025-06-03";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.395032;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:1:{s:4:"date";s:10:"2025-06-03";}s:4:"POST";a:0:{}s:6:"COOKIE";a:2:{s:9:"PHPSESSID";s:32:"5kmna394fkk624c3mdo5a4tcspgksban";s:5:"_csrf";s:130:"122dfc0a9fdf737eed675d94f36c22b75683d1d1010d527a00b1a971721d03fda:2:{i:0;s:5:"_csrf";i:1;s:32:"mQAy1MIxX8gNk5d7r1CbyrNJqKwlDd6G";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:1:{s:7:"__flash";a:0:{}}}";s:4:"user";s:2147:"a:5:{s:2:"id";i:7;s:8:"identity";a:8:{s:2:"id";s:1:"7";s:8:"username";s:14:"'manufacturer'";s:9:"full_name";s:20:"'Ishlab chiqaruvchi'";s:4:"role";s:1:"0";s:12:"access_token";s:42:"'681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";s:8:"password";s:62:"'$2y$13$8ymQqJl5qbjAvbz9d5fbFuOFZGcDCeN6PAzjPCDDcPlYo7inyMDbG'";s:10:"created_at";s:21:"'2025-03-17 10:58:05'";s:10:"deleted_at";s:4:"null";}s:10:"attributes";a:8:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:10:"Логин";}i:2;a:2:{s:9:"attribute";s:9:"full_name";s:5:"label";s:9:"Full Name";}i:3;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:4;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}i:5;a:2:{s:9:"attribute";s:8:"password";s:5:"label";s:10:"Парол";}i:6;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:7;a:2:{s:9:"attribute";s:10:"deleted_at";s:5:"label";s:10:"Deleted At";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";N;s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:1:{s:12:"manufacturer";a:7:{s:4:"type";i:1;s:4:"name";s:12:"manufacturer";s:11:"description";s:12:"Manufacturer";s:8:"ruleName";N;s:4:"data";s:4:"null";s:9:"createdAt";i:**********;s:9:"updatedAt";i:**********;}}s:10:"modelClass";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-1";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"683ea237739f8";s:3:"url";s:68:"http://silver/api/manufacter/send-to-material-defect?date=2025-06-03";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:**********.395032;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9277544;s:14:"processingTime";d:0.26956605911254883;}s:10:"exceptions";a:0:{}}