a:14:{s:6:"config";s:4971:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:10:"2.0.52-dev";s:11:"application";a:8:{s:3:"yii";s:10:"2.0.52-dev";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:2:"uz";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:16:{s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/base";s:67:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:62:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:60:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-grid/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:77:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-jui/src";}}s:33:"2amigos/yii2-arrayquery-component";a:3:{s:4:"name";s:33:"2amigos/yii2-arrayquery-component";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@dosamigos/arrayquery";s:75:"D:\OSPanel\domains\silverzavod\vendor/2amigos/yii2-arrayquery-component/src";}}s:17:"yii2mod/yii2-rbac";a:3:{s:4:"name";s:17:"yii2mod/yii2-rbac";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii2mod/rbac";s:55:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-rbac";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.25.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:10:"@yii/faker";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.6.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:23:"3.0.9999999.9999999-dev";s:5:"alias";a:10:{s:10:"@yii/queue";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:71:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:72:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/file";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:76:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:78:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:81:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:68:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.11.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap4/src";}}s:20:"yii2mod/yii2-swagger";a:3:{s:4:"name";s:20:"yii2mod/yii2-swagger";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:16:"@yii2mod/swagger";s:58:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-swagger";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:51889:"a:1:{s:8:"messages";a:47:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.400703;i:4;a:0:{}i:5;i:2611976;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.403349;i:4;a:0:{}i:5;i:2728984;}i:2;a:6:{i:0;s:53:"Bootstrap with app\components\SessionTimeoutComponent";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.405028;i:4;a:0:{}i:5;i:2770192;}i:3;a:6:{i:0;s:22:"Bootstrap with Closure";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.405043;i:4;a:0:{}i:5;i:2770568;}i:4;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.432918;i:4;a:0:{}i:5;i:3915768;}i:5;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.449372;i:4;a:0:{}i:5;i:4726024;}i:6;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.458127;i:4;a:0:{}i:5;i:5115688;}i:7;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.466041;i:4;a:0:{}i:5;i:5580096;}i:8;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.466716;i:4;a:0:{}i:5;i:5607488;}i:20;a:6:{i:0;s:64:"Route requested: 'api/manufacter/update-send-to-material-defect'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.469795;i:4;a:0:{}i:5;i:5781176;}i:21;a:6:{i:0;s:19:"Loading module: api";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.469806;i:4;a:0:{}i:5;i:5782840;}i:22;a:6:{i:0;s:59:"Route to run: api/manufacter/update-send-to-material-defect";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.476829;i:4;a:0:{}i:5;i:6129384;}i:23;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.505711;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7470280;}i:26;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.554505;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8001680;}i:29;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.582497;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8047592;}i:32;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.589915;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8343400;}i:35;a:6:{i:0;s:55:"User '7' logged in from 127.0.0.1. Session not enabled.";i:1;i:4;i:2;s:19:"yii\web\User::login";i:3;d:**********.597805;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8630048;}i:36;a:6:{i:0;s:65:"Rate limit skipped: "user" does not implement RateLimitInterface.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:**********.597845;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8630640;}i:37;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.599912;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8818224;}i:40;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.604364;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8824848;}i:43;a:6:{i:0;s:27:"Checking role: manufacturer";i:1;i:8;i:2;s:40:"yii\rbac\DbManager::checkAccessRecursive";i:3;d:**********.607788;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8828240;}i:44;a:6:{i:0;s:100:"Running action: app\modules\api\controllers\ManufacterController::actionUpdateSendToMaterialDefect()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.607819;i:4;a:0:{}i:5;i:8827400;}i:45;a:6:{i:0;s:105:"Failed to set unsafe attribute 'material_defect_id' in 'app\modules\api\models\SendToMaterialDefectForm'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:**********.610729;i:4;a:2:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:206;s:8:"function";s:4:"load";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9089400;}i:46;a:6:{i:0;s:91:"Failed to set unsafe attribute 'type' in 'app\modules\api\models\SendToMaterialDefectForm'.";i:1;i:8;i:2;s:33:"yii\base\Model::onUnsafeAttribute";i:3;d:**********.610741;i:4;a:2:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:206;s:8:"function";s:4:"load";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9091032;}i:47;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.611328;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:208;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9140264;}i:50;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.617009;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:208;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9151056;}i:53;a:6:{i:0;s:37:"SELECT * FROM "material" WHERE "id"=2";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.62089;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:208;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9158128;}i:56;a:6:{i:0;s:17:"Begin transaction";i:1;i:8;i:2;s:25:"yii\db\Transaction::begin";i:3;d:**********.624095;i:4;a:2:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:216;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9178640;}i:57;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_defect'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.624829;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:242;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9220408;}i:60;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_defect'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.630846;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:242;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9233368;}i:63;a:6:{i:0;s:96:"SELECT * FROM "material_defect" WHERE ("id"=16) AND ("deleted_at" IS NULL) AND ("add_user_id"=7)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.634228;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:242;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9242088;}i:66;a:6:{i:0;s:100:"SELECT * FROM "tracking" WHERE ("process_id"=16) AND ("progress_type"=10) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.637365;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:265;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9278296;}i:69;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.639844;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:265;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9290488;}i:72;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.644896;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:265;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9301288;}i:75;a:6:{i:0;s:98:"SELECT SUM(quantity) FROM "material_production" WHERE ("material_id"=2) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.64946;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:402;s:8:"function";s:3:"sum";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:273;s:8:"function";s:28:"validateMaterialInProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9327992;}i:78;a:6:{i:0;s:122:"SELECT * FROM "material_production" WHERE ("material_id"=1) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-03')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.652245;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:426;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9334416;}i:81;a:6:{i:0;s:2825:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_production'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.653487;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:430;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9342888;}i:84;a:6:{i:0;s:887:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_production'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.658747;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:430;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9353256;}i:87;a:6:{i:0;s:166:"INSERT INTO "material_production" ("material_id", "quantity", "created_at", "updated_at") VALUES (1, '2', '2025-06-03 11:19:52', '2025-06-03 11:19:52') RETURNING "id"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.663466;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:438;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9405576;}i:90;a:6:{i:0;s:131:"SELECT * FROM "material_production" WHERE (("material_id"=2) AND ("deleted_at" IS NULL)) AND ("quantity" > 0) ORDER BY "created_at"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.666684;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:464;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:279;s:8:"function";s:26:"decreaseMaterialProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9437072;}i:93;a:6:{i:0;s:97:"UPDATE "material_production" SET "quantity"='6', "updated_at"='2025-06-03 11:19:52' WHERE "id"=25";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.668562;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:482;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:279;s:8:"function";s:26:"decreaseMaterialProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9449928;}i:96;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=2)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.670762;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:289;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9500272;}i:99;a:6:{i:0;s:137:"UPDATE "material_defect" SET "material_id"=2, "quantity"=1, "accepted_user_id"=NULL, "description"=NULL, "accepted_at"=NULL WHERE "id"=16";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.672676;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:289;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9533880;}i:102;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.674331;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:300;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9545816;}i:105;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.679946;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:300;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9556608;}i:108;a:6:{i:0;s:266:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (7, 'update_send_to_defect', 'material_defect', '"16"'::jsonb, '"{\"material_id\":2,\"quantity\":1,\"description\":null}"'::jsonb, '2025-06-03 11:19:52')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.685101;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:300;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9573744;}i:111;a:6:{i:0;s:18:"Commit transaction";i:1;i:8;i:2;s:26:"yii\db\Transaction::commit";i:3;d:**********.687889;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:304;s:8:"function";s:6:"commit";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9573776;}}}";s:9:"profiling";s:91221:"a:3:{s:6:"memory";i:9903296;s:4:"time";d:0.3172180652618408;s:8:"messages";a:54:{i:24;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.505748;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7471784;}i:25;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.552955;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7474088;}i:27;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.554555;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8003632;}i:28;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.580515;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8019368;}i:30;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.582536;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8049456;}i:31;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.586174;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8051392;}i:33;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.589946;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8347664;}i:34;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.593742;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8350424;}i:38;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.599939;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8820832;}i:39;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.603712;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8823000;}i:41;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.60439;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8827456;}i:42;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.607194;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8829560;}i:48;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.611364;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:208;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9142504;}i:49;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.616215;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:208;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9157016;}i:51;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.617056;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:208;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9153296;}i:52;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.62042;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:208;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9156504;}i:54;a:6:{i:0;s:37:"SELECT * FROM "material" WHERE "id"=2";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.620917;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:208;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9161488;}i:55;a:6:{i:0;s:37:"SELECT * FROM "material" WHERE "id"=2";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.623227;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:208;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9164384;}i:58;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_defect'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.624866;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:242;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9222648;}i:59;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_defect'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.630261;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:242;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9243664;}i:61;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_defect'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.630884;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:242;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9235608;}i:62;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_defect'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.633766;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:242;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9239400;}i:64;a:6:{i:0;s:96:"SELECT * FROM "material_defect" WHERE ("id"=16) AND ("deleted_at" IS NULL) AND ("add_user_id"=7)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.634265;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:242;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9244912;}i:65;a:6:{i:0;s:96:"SELECT * FROM "material_defect" WHERE ("id"=16) AND ("deleted_at" IS NULL) AND ("add_user_id"=7)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.636496;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:242;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9254256;}i:67;a:6:{i:0;s:100:"SELECT * FROM "tracking" WHERE ("process_id"=16) AND ("progress_type"=10) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.637392;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:265;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9281760;}i:68;a:6:{i:0;s:100:"SELECT * FROM "tracking" WHERE ("process_id"=16) AND ("progress_type"=10) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.6395;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:265;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9284696;}i:70;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.639879;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:265;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9292728;}i:71;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.644353;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:265;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9307280;}i:73;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.644922;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:265;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9303528;}i:74;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.648552;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:265;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9305840;}i:76;a:6:{i:0;s:98:"SELECT SUM(quantity) FROM "material_production" WHERE ("material_id"=2) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.649488;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:402;s:8:"function";s:3:"sum";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:273;s:8:"function";s:28:"validateMaterialInProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9331400;}i:77;a:6:{i:0;s:98:"SELECT SUM(quantity) FROM "material_production" WHERE ("material_id"=2) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.651615;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:402;s:8:"function";s:3:"sum";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:273;s:8:"function";s:28:"validateMaterialInProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9333416;}i:79;a:6:{i:0;s:122:"SELECT * FROM "material_production" WHERE ("material_id"=1) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-03')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.652273;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:426;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9337240;}i:80;a:6:{i:0;s:122:"SELECT * FROM "material_production" WHERE ("material_id"=1) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-03')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.653118;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:426;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9339616;}i:82;a:6:{i:0;s:2825:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_production'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.653522;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:430;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9345128;}i:83;a:6:{i:0;s:2825:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_production'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.658194;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:430;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9358248;}i:85;a:6:{i:0;s:887:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_production'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.658776;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:430;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9355496;}i:86;a:6:{i:0;s:887:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_production'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.661634;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:430;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9357808;}i:88;a:6:{i:0;s:166:"INSERT INTO "material_production" ("material_id", "quantity", "created_at", "updated_at") VALUES (1, '2', '2025-06-03 11:19:52', '2025-06-03 11:19:52') RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.663481;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:438;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9407080;}i:89;a:6:{i:0;s:166:"INSERT INTO "material_production" ("material_id", "quantity", "created_at", "updated_at") VALUES (1, '2', '2025-06-03 11:19:52', '2025-06-03 11:19:52') RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.66523;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:438;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9409560;}i:91;a:6:{i:0;s:131:"SELECT * FROM "material_production" WHERE (("material_id"=2) AND ("deleted_at" IS NULL)) AND ("quantity" > 0) ORDER BY "created_at"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.666713;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:464;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:279;s:8:"function";s:26:"decreaseMaterialProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9439928;}i:92;a:6:{i:0;s:131:"SELECT * FROM "material_production" WHERE (("material_id"=2) AND ("deleted_at" IS NULL)) AND ("quantity" > 0) ORDER BY "created_at"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.667906;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:464;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:279;s:8:"function";s:26:"decreaseMaterialProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9443640;}i:94;a:6:{i:0;s:97:"UPDATE "material_production" SET "quantity"='6', "updated_at"='2025-06-03 11:19:52' WHERE "id"=25";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.668597;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:482;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:279;s:8:"function";s:26:"decreaseMaterialProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9453384;}i:95;a:6:{i:0;s:97:"UPDATE "material_production" SET "quantity"='6', "updated_at"='2025-06-03 11:19:52' WHERE "id"=25";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.669309;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:482;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:279;s:8:"function";s:26:"decreaseMaterialProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9455432;}i:97;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=2)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.670791;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:289;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9503648;}i:98;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=2)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.671651;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:289;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9505632;}i:100;a:6:{i:0;s:137:"UPDATE "material_defect" SET "material_id"=2, "quantity"=1, "accepted_user_id"=NULL, "description"=NULL, "accepted_at"=NULL WHERE "id"=16";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.672702;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:289;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9536832;}i:101;a:6:{i:0;s:137:"UPDATE "material_defect" SET "material_id"=2, "quantity"=1, "accepted_user_id"=NULL, "description"=NULL, "accepted_at"=NULL WHERE "id"=16";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.673655;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:289;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9539000;}i:103;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.674364;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:300;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9548056;}i:104;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.679488;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:300;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9562536;}i:106;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.67998;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:300;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9558848;}i:107;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.683908;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:300;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9562032;}i:109;a:6:{i:0;s:266:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (7, 'update_send_to_defect', 'material_defect', '"16"'::jsonb, '"{\"material_id\":2,\"quantity\":1,\"description\":null}"'::jsonb, '2025-06-03 11:19:52')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.685143;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:300;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9577368;}i:110;a:6:{i:0;s:266:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (7, 'update_send_to_defect', 'material_defect', '"16"'::jsonb, '"{\"material_id\":2,\"quantity\":1,\"description\":null}"'::jsonb, '2025-06-03 11:19:52')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.687605;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:300;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9579504;}}}";s:2:"db";s:89976:"a:1:{s:8:"messages";a:52:{i:27;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.554555;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8003632;}i:28;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.580515;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8019368;}i:30;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.582536;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8049456;}i:31;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.586174;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8051392;}i:33;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.589946;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8347664;}i:34;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.593742;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8350424;}i:38;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.599939;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8820832;}i:39;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.603712;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8823000;}i:41;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.60439;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8827456;}i:42;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.607194;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8829560;}i:48;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.611364;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:208;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9142504;}i:49;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.616215;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:208;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9157016;}i:51;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.617056;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:208;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9153296;}i:52;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.62042;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:208;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9156504;}i:54;a:6:{i:0;s:37:"SELECT * FROM "material" WHERE "id"=2";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.620917;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:208;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9161488;}i:55;a:6:{i:0;s:37:"SELECT * FROM "material" WHERE "id"=2";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.623227;i:4;a:3:{i:0;a:5:{s:4:"file";s:78:"D:\OSPanel\domains\silverzavod\modules\api\models\SendToMaterialDefectForm.php";s:4:"line";i:48;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:208;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9164384;}i:58;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_defect'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.624866;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:242;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9222648;}i:59;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_defect'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.630261;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:242;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9243664;}i:61;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_defect'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.630884;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:242;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9235608;}i:62;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_defect'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.633766;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:242;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9239400;}i:64;a:6:{i:0;s:96:"SELECT * FROM "material_defect" WHERE ("id"=16) AND ("deleted_at" IS NULL) AND ("add_user_id"=7)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.634265;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:242;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9244912;}i:65;a:6:{i:0;s:96:"SELECT * FROM "material_defect" WHERE ("id"=16) AND ("deleted_at" IS NULL) AND ("add_user_id"=7)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.636496;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:242;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9254256;}i:67;a:6:{i:0;s:100:"SELECT * FROM "tracking" WHERE ("process_id"=16) AND ("progress_type"=10) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.637392;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:265;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9281760;}i:68;a:6:{i:0;s:100:"SELECT * FROM "tracking" WHERE ("process_id"=16) AND ("progress_type"=10) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.6395;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:265;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9284696;}i:70;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.639879;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:265;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9292728;}i:71;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.644353;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:265;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9307280;}i:73;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.644922;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:265;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9303528;}i:74;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.648552;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:265;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9305840;}i:76;a:6:{i:0;s:98:"SELECT SUM(quantity) FROM "material_production" WHERE ("material_id"=2) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.649488;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:402;s:8:"function";s:3:"sum";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:273;s:8:"function";s:28:"validateMaterialInProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9331400;}i:77;a:6:{i:0;s:98:"SELECT SUM(quantity) FROM "material_production" WHERE ("material_id"=2) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.651615;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:402;s:8:"function";s:3:"sum";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:273;s:8:"function";s:28:"validateMaterialInProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9333416;}i:79;a:6:{i:0;s:122:"SELECT * FROM "material_production" WHERE ("material_id"=1) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-03')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.652273;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:426;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9337240;}i:80;a:6:{i:0;s:122:"SELECT * FROM "material_production" WHERE ("material_id"=1) AND ("deleted_at" IS NULL) AND (DATE(created_at)='2025-06-03')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.653118;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:426;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9339616;}i:82;a:6:{i:0;s:2825:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_production'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.653522;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:430;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9345128;}i:83;a:6:{i:0;s:2825:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_production'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.658194;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:430;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9358248;}i:85;a:6:{i:0;s:887:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_production'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.658776;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:430;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9355496;}i:86;a:6:{i:0;s:887:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_production'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.661634;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:430;s:8:"function";s:5:"__set";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9357808;}i:88;a:6:{i:0;s:166:"INSERT INTO "material_production" ("material_id", "quantity", "created_at", "updated_at") VALUES (1, '2', '2025-06-03 11:19:52', '2025-06-03 11:19:52') RETURNING "id"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.663481;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:438;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9407080;}i:89;a:6:{i:0;s:166:"INSERT INTO "material_production" ("material_id", "quantity", "created_at", "updated_at") VALUES (1, '2', '2025-06-03 11:19:52', '2025-06-03 11:19:52') RETURNING "id"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.66523;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:438;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:276;s:8:"function";s:26:"returnMaterialToProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9409560;}i:91;a:6:{i:0;s:131:"SELECT * FROM "material_production" WHERE (("material_id"=2) AND ("deleted_at" IS NULL)) AND ("quantity" > 0) ORDER BY "created_at"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.666713;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:464;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:279;s:8:"function";s:26:"decreaseMaterialProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9439928;}i:92;a:6:{i:0;s:131:"SELECT * FROM "material_production" WHERE (("material_id"=2) AND ("deleted_at" IS NULL)) AND ("quantity" > 0) ORDER BY "created_at"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.667906;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:464;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:279;s:8:"function";s:26:"decreaseMaterialProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9443640;}i:94;a:6:{i:0;s:97:"UPDATE "material_production" SET "quantity"='6', "updated_at"='2025-06-03 11:19:52' WHERE "id"=25";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.668597;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:482;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:279;s:8:"function";s:26:"decreaseMaterialProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9453384;}i:95;a:6:{i:0;s:97:"UPDATE "material_production" SET "quantity"='6', "updated_at"='2025-06-03 11:19:52' WHERE "id"=25";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.669309;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:482;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:279;s:8:"function";s:26:"decreaseMaterialProduction";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9455432;}i:97;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=2)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.670791;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:289;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9503648;}i:98;a:6:{i:0;s:63:"SELECT EXISTS(SELECT * FROM "material" WHERE "material"."id"=2)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.671651;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:289;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9505632;}i:100;a:6:{i:0;s:137:"UPDATE "material_defect" SET "material_id"=2, "quantity"=1, "accepted_user_id"=NULL, "description"=NULL, "accepted_at"=NULL WHERE "id"=16";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.672702;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:289;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9536832;}i:101;a:6:{i:0;s:137:"UPDATE "material_defect" SET "material_id"=2, "quantity"=1, "accepted_user_id"=NULL, "description"=NULL, "accepted_at"=NULL WHERE "id"=16";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.673655;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:289;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:483;s:8:"function";s:20:"updateMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9539000;}i:103;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.674364;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:300;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9548056;}i:104;a:6:{i:0;s:2817:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'action_logs'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.679488;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:300;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9562536;}i:106;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.67998;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:300;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9558848;}i:107;a:6:{i:0;s:879:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='action_logs'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.683908;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:21;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:300;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9562032;}i:109;a:6:{i:0;s:266:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (7, 'update_send_to_defect', 'material_defect', '"16"'::jsonb, '"{\"material_id\":2,\"quantity\":1,\"description\":null}"'::jsonb, '2025-06-03 11:19:52')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.685143;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:300;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9577368;}i:110;a:6:{i:0;s:266:"INSERT INTO "action_logs" ("user_id", "action_type", "table_name", "old_data", "new_data", "action_time") VALUES (7, 'update_send_to_defect', 'material_defect', '"16"'::jsonb, '"{\"material_id\":2,\"quantity\":1,\"description\":null}"'::jsonb, '2025-06-03 11:19:52')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.687605;i:4;a:3:{i:0;a:5:{s:4:"file";s:61:"D:\OSPanel\domains\silverzavod\common\models\ActionLogger.php";s:4:"line";i:22;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:300;s:8:"function";s:9:"actionLog";s:5:"class";s:30:"app\common\models\ActionLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\UpdateSendToMaterialDefectService.php";s:4:"line";i:219;s:8:"function";s:18:"updateDefectRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\UpdateSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9579504;}}}";s:5:"event";s:11157:"a:60:{i:0;a:5:{s:4:"time";d:**********.46911;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:**********.477203;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:**********.477227;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\api\ApiModule";}i:3;a:5:{s:4:"time";d:**********.496679;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:4;a:5:{s:4:"time";d:**********.552939;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:5;a:5:{s:4:"time";d:**********.594231;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\modules\api\models\Users";}i:6;a:5:{s:4:"time";d:**********.594287;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\modules\api\models\Users";}i:7;a:5:{s:4:"time";d:**********.594559;s:4:"name";s:11:"beforeLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:8;a:5:{s:4:"time";d:**********.597677;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:9;a:5:{s:4:"time";d:**********.597699;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:10;a:5:{s:4:"time";d:**********.597707;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:11;a:5:{s:4:"time";d:**********.597714;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:12;a:5:{s:4:"time";d:**********.59772;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:13;a:5:{s:4:"time";d:**********.597725;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:14;a:5:{s:4:"time";d:**********.597731;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:15;a:5:{s:4:"time";d:**********.597825;s:4:"name";s:10:"afterLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:16;a:5:{s:4:"time";d:**********.597873;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:48:"app\modules\api\controllers\ManufacterController";}i:17;a:5:{s:4:"time";d:**********.61086;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:47:"app\modules\api\models\SendToMaterialDefectForm";}i:18;a:5:{s:4:"time";d:**********.611266;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:19;a:5:{s:4:"time";d:**********.623486;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Material";}i:20;a:5:{s:4:"time";d:**********.623534;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Material";}i:21;a:5:{s:4:"time";d:**********.623583;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:47:"app\modules\api\models\SendToMaterialDefectForm";}i:22;a:5:{s:4:"time";d:**********.624115;s:4:"name";s:16:"beginTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:23;a:5:{s:4:"time";d:**********.62475;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:24;a:5:{s:4:"time";d:**********.636774;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\common\models\MaterialDefect";}i:25;a:5:{s:4:"time";d:**********.636822;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\common\models\MaterialDefect";}i:26;a:5:{s:4:"time";d:**********.637269;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:27;a:5:{s:4:"time";d:**********.639775;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:28;a:5:{s:4:"time";d:**********.64888;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:29;a:5:{s:4:"time";d:**********.649348;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:30;a:5:{s:4:"time";d:**********.652089;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:31;a:5:{s:4:"time";d:**********.653415;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:32;a:5:{s:4:"time";d:**********.661967;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:33;a:5:{s:4:"time";d:**********.663329;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:34;a:5:{s:4:"time";d:**********.663343;s:4:"name";s:12:"beforeInsert";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:35;a:5:{s:4:"time";d:**********.665638;s:4:"name";s:11:"afterInsert";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:36;a:5:{s:4:"time";d:**********.665678;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:37;a:5:{s:4:"time";d:**********.668132;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:38;a:5:{s:4:"time";d:**********.668172;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:39;a:5:{s:4:"time";d:**********.668189;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:40;a:5:{s:4:"time";d:**********.668194;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:41;a:5:{s:4:"time";d:**********.668228;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:42;a:5:{s:4:"time";d:**********.668423;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:43;a:5:{s:4:"time";d:**********.668435;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:44;a:5:{s:4:"time";d:**********.669547;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"app\common\models\MaterialProduction";}i:45;a:5:{s:4:"time";d:**********.669582;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\common\models\MaterialDefect";}i:46;a:5:{s:4:"time";d:**********.670636;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:47;a:5:{s:4:"time";d:**********.670672;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:48;a:5:{s:4:"time";d:**********.671995;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\common\models\MaterialDefect";}i:49;a:5:{s:4:"time";d:**********.672012;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\common\models\MaterialDefect";}i:50;a:5:{s:4:"time";d:**********.67395;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\common\models\MaterialDefect";}i:51;a:5:{s:4:"time";d:**********.690702;s:4:"name";s:17:"commitTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:52;a:5:{s:4:"time";d:**********.691653;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:53;a:5:{s:4:"time";d:**********.691957;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:48:"app\modules\api\controllers\ManufacterController";}i:54;a:5:{s:4:"time";d:**********.692437;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\api\ApiModule";}i:55;a:5:{s:4:"time";d:**********.692445;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:56;a:5:{s:4:"time";d:**********.692453;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:57;a:5:{s:4:"time";d:**********.692459;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:58;a:5:{s:4:"time";d:**********.693899;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:59;a:5:{s:4:"time";d:**********.694039;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:**********.380569;s:3:"end";d:**********.698569;s:6:"memory";i:9947456;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:2257:"a:3:{s:8:"messages";a:11:{i:9;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.469691;i:4;a:0:{}i:5;i:5773264;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.46971;i:4;a:0:{}i:5;i:5774016;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.46972;i:4;a:0:{}i:5;i:5774768;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.469726;i:4;a:0:{}i:5;i:5775520;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.469733;i:4;a:0:{}i:5;i:5776272;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.469739;i:4;a:0:{}i:5;i:5777024;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:5:"login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.469745;i:4;a:0:{}i:5;i:5777776;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:6:"logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.469752;i:4;a:0:{}i:5;i:5778528;}i:17;a:6:{i:0;a:3:{s:4:"rule";s:13:"site/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.469759;i:4;a:0:{}i:5;i:5779920;}i:18;a:6:{i:0;s:69:"Request parsed with URL rule: api/<controller:[\w-]+>/<action:[\w-]+>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:**********.469781;i:4;a:0:{}i:5;i:5782040;}i:19;a:6:{i:0;a:3:{s:4:"rule";s:39:"api/<controller:[\w-]+>/<action:[\w-]+>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.469787;i:4;a:0:{}i:5;i:5781832;}}s:5:"route";s:45:"api/manufacter/update-send-to-material-defect";s:6:"action";s:84:"app\modules\api\controllers\ManufacterController::actionUpdateSendToMaterialDefect()";}";s:7:"request";s:4694:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:11:{s:13:"authorization";s:47:"Bearer 681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy";s:12:"content-type";s:16:"application/json";s:10:"user-agent";s:21:"PostmanRuntime/7.44.0";s:6:"accept";s:3:"*/*";s:13:"cache-control";s:8:"no-cache";s:13:"postman-token";s:36:"3c3f6e28-2af2-4a51-b1fe-7c3e1947d24b";s:4:"host";s:6:"silver";s:15:"accept-encoding";s:17:"gzip, deflate, br";s:10:"connection";s:10:"keep-alive";s:14:"content-length";s:3:"192";s:6:"cookie";s:216:"PHPSESSID=5kmna394fkk624c3mdo5a4tcspgksban; _csrf=7d2e73e343cfe5007a0e3b2fbec224b7397a0cec4db1009f7f376dae337c2968a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22pB93555YSe-swaOITQzxkWUmXpyGhXTP%22%3B%7D";}s:15:"responseHeaders";a:9:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:4:"Vary";s:6:"Accept";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"683e940870191";s:16:"X-Debug-Duration";s:3:"314";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=683e940870191";s:10:"Set-Cookie";s:204:"_csrf=412be5542c2de8c88844c03b15294a41ce660b5fa1ab2c98b87d5ef746a1e108a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22kCZoX-xQtrYXUsRoySWrINRDvpqH-rD5%22%3B%7D; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:45:"api/manufacter/update-send-to-material-defect";s:6:"action";s:84:"app\modules\api\controllers\ManufacterController::actionUpdateSendToMaterialDefect()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:16:"application/json";s:3:"Raw";s:192:"{
    "material_defect_id": 16,
    "type": "defect",
    "materials": [
        {
            "material_id": 2,
            "quantity": 1
        }
    ],
    "is_returned": false
}";s:7:"Decoded";a:4:{s:18:"material_defect_id";i:16;s:4:"type";s:6:"defect";s:9:"materials";a:1:{i:0;a:2:{s:11:"material_id";i:2;s:8:"quantity";i:1;}}s:11:"is_returned";b:0;}}s:6:"SERVER";a:39:{s:15:"REDIRECT_STATUS";s:3:"200";s:12:"CONTENT_TYPE";s:16:"application/json";s:15:"HTTP_USER_AGENT";s:21:"PostmanRuntime/7.44.0";s:11:"HTTP_ACCEPT";s:3:"*/*";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:18:"HTTP_POSTMAN_TOKEN";s:36:"3c3f6e28-2af2-4a51-b1fe-7c3e1947d24b";s:9:"HTTP_HOST";s:6:"silver";s:20:"HTTP_ACCEPT_ENCODING";s:17:"gzip, deflate, br";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:14:"CONTENT_LENGTH";s:3:"192";s:11:"HTTP_COOKIE";s:216:"PHPSESSID=5kmna394fkk624c3mdo5a4tcspgksban; _csrf=7d2e73e343cfe5007a0e3b2fbec224b7397a0cec4db1009f7f376dae337c2968a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22pB93555YSe-swaOITQzxkWUmXpyGhXTP%22%3B%7D";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:6:"silver";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:44:"D:/OSPanel/domains/silverzavod/web/index.php";s:11:"REMOTE_PORT";s:5:"62352";s:12:"REDIRECT_URL";s:46:"/api/manufacter/update-send-to-material-defect";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:46:"/api/manufacter/update-send-to-material-defect";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.354968;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:2:{s:9:"PHPSESSID";s:32:"5kmna394fkk624c3mdo5a4tcspgksban";s:5:"_csrf";s:130:"7d2e73e343cfe5007a0e3b2fbec224b7397a0cec4db1009f7f376dae337c2968a:2:{i:0;s:5:"_csrf";i:1;s:32:"pB93555YSe-swaOITQzxkWUmXpyGhXTP";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:1:{s:7:"__flash";a:0:{}}}";s:4:"user";s:2147:"a:5:{s:2:"id";i:7;s:8:"identity";a:8:{s:2:"id";s:1:"7";s:8:"username";s:14:"'manufacturer'";s:9:"full_name";s:20:"'Ishlab chiqaruvchi'";s:4:"role";s:1:"0";s:12:"access_token";s:42:"'681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";s:8:"password";s:62:"'$2y$13$8ymQqJl5qbjAvbz9d5fbFuOFZGcDCeN6PAzjPCDDcPlYo7inyMDbG'";s:10:"created_at";s:21:"'2025-03-17 10:58:05'";s:10:"deleted_at";s:4:"null";}s:10:"attributes";a:8:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:10:"Логин";}i:2;a:2:{s:9:"attribute";s:9:"full_name";s:5:"label";s:9:"Full Name";}i:3;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:4;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}i:5;a:2:{s:9:"attribute";s:8:"password";s:5:"label";s:10:"Парол";}i:6;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:7;a:2:{s:9:"attribute";s:10:"deleted_at";s:5:"label";s:10:"Deleted At";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";N;s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:1:{s:12:"manufacturer";a:7:{s:4:"type";i:1;s:4:"name";s:12:"manufacturer";s:11:"description";s:12:"Manufacturer";s:8:"ruleName";N;s:4:"data";s:4:"null";s:9:"createdAt";i:**********;s:9:"updatedAt";i:**********;}}s:10:"modelClass";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-1";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"683e940870191";s:3:"url";s:59:"http://silver/api/manufacter/update-send-to-material-defect";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:**********.354968;s:10:"statusCode";i:200;s:8:"sqlCount";i:26;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9903296;s:14:"processingTime";d:0.3172180652618408;}s:10:"exceptions";a:0:{}}