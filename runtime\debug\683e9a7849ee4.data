a:14:{s:6:"config";s:4971:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:10:"2.0.52-dev";s:11:"application";a:8:{s:3:"yii";s:10:"2.0.52-dev";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:2:"uz";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:16:{s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/base";s:67:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:62:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:60:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-grid/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:77:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-jui/src";}}s:33:"2amigos/yii2-arrayquery-component";a:3:{s:4:"name";s:33:"2amigos/yii2-arrayquery-component";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@dosamigos/arrayquery";s:75:"D:\OSPanel\domains\silverzavod\vendor/2amigos/yii2-arrayquery-component/src";}}s:17:"yii2mod/yii2-rbac";a:3:{s:4:"name";s:17:"yii2mod/yii2-rbac";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii2mod/rbac";s:55:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-rbac";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.25.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:10:"@yii/faker";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.6.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:23:"3.0.9999999.9999999-dev";s:5:"alias";a:10:{s:10:"@yii/queue";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:71:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:72:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/file";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:76:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:78:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:81:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:68:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.11.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap4/src";}}s:20:"yii2mod/yii2-swagger";a:3:{s:4:"name";s:20:"yii2mod/yii2-swagger";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:16:"@yii2mod/swagger";s:58:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-swagger";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:16489:"a:1:{s:8:"messages";a:33:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748933240.148297;i:4;a:0:{}i:5;i:2613048;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748933240.149493;i:4;a:0:{}i:5;i:2730056;}i:2;a:6:{i:0;s:53:"Bootstrap with app\components\SessionTimeoutComponent";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748933240.149979;i:4;a:0:{}i:5;i:2771264;}i:3;a:6:{i:0;s:22:"Bootstrap with Closure";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748933240.149986;i:4;a:0:{}i:5;i:2771640;}i:4;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1748933240.165722;i:4;a:0:{}i:5;i:3916840;}i:5;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1748933240.181708;i:4;a:0:{}i:5;i:4727328;}i:6;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1748933240.201941;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6031960;}i:9;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.24849;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6169376;}i:12;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.282988;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6279696;}i:15;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.289779;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6574464;}i:18;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748933240.302175;i:4;a:0:{}i:5;i:7264832;}i:19;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1748933240.306218;i:4;a:0:{}i:5;i:8038056;}i:20;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748933240.306601;i:4;a:0:{}i:5;i:8062888;}i:45;a:6:{i:0;s:53:"Route requested: 'backend/inventory-adjustment/index'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1748933240.307462;i:4;a:0:{}i:5;i:8118768;}i:46;a:6:{i:0;s:23:"Loading module: backend";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1748933240.307472;i:4;a:0:{}i:5;i:8120408;}i:47;a:6:{i:0;s:48:"Route to run: backend/inventory-adjustment/index";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1748933240.309035;i:4;a:0:{}i:5;i:8239024;}i:48;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.312751;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8719520;}i:51;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.316762;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8726136;}i:54;a:6:{i:0;s:20:"Checking role: admin";i:1;i:8;i:2;s:40:"yii\rbac\DbManager::checkAccessRecursive";i:3;d:1748933240.32011;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8729512;}i:55;a:6:{i:0;s:92:"Running action: app\modules\backend\controllers\InventoryAdjustmentController::actionIndex()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1748933240.320137;i:4;a:0:{}i:5;i:8728680;}i:56;a:6:{i:0;s:462:"
            SELECT
                mp.material_id,
                m.name as material_name,
                SUM(mp.quantity) as quantity,
                MAX(mp.created_at) as created_at
            FROM material_production mp
            LEFT JOIN material m ON m.id = mp.material_id
            WHERE mp.deleted_at IS NULL
            GROUP BY mp.material_id, m.name
            HAVING SUM(mp.quantity) > 0
            ORDER BY m.name ASC
        ";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.320169;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:43;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8732704;}i:59;a:6:{i:0;s:459:"
            SELECT
                ms.material_id,
                m.name as material_name,
                SUM(ms.quantity) as quantity,
                MAX(ms.created_at) as created_at
            FROM material_storage ms
            LEFT JOIN material m ON m.id = ms.material_id
            WHERE ms.deleted_at IS NULL
            GROUP BY ms.material_id, m.name
            HAVING SUM(ms.quantity) > 0
            ORDER BY m.name ASC
        ";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.325439;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:59;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8738600;}i:62;a:6:{i:0;s:613:"
            SELECT
                ps.product_id,
                p.name as product_name,
                SUM(ps.quantity) as quantity,
                SUM(ps.quantity_block) as quantity_block,
                MAX(ps.enter_date) as enter_date
            FROM product_storage ps
            LEFT JOIN product p ON p.id = ps.product_id
            WHERE ps.deleted_at IS NULL
                AND ps.accepted_at IS NOT NULL
                AND ps.accepted_user_id IS NOT NULL
            GROUP BY ps.product_id, p.name
            HAVING SUM(ps.quantity) > 0
            ORDER BY p.name ASC
        ";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.328442;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:78;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8745024;}i:65;a:6:{i:0;s:78:"SELECT "id", "name" FROM "material" WHERE "deleted_at" IS NULL ORDER BY "name"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.334182;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:86;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8791352;}i:68;a:6:{i:0;s:85:"SELECT "id", "name", "size" FROM "product" WHERE "deleted_at" IS NULL ORDER BY "name"";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.33586;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:94;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8823944;}i:71;a:6:{i:0;s:104:"Rendering view file: D:\OSPanel\domains\silverzavod\modules\backend\views\inventory-adjustment\index.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1748933240.338928;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:101;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:8961320;}i:72;a:6:{i:0;s:74:"Rendering view file: D:\OSPanel\domains\silverzavod\views\layouts\main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1748933240.361529;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:101;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9780368;}i:73;a:6:{i:0;s:74:"Rendering view file: D:\OSPanel\domains\silverzavod\views\layouts\menu.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1748933240.363093;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:23;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:101;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9841728;}i:74;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='sales'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.364195;i:4;a:3:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\menu.php";s:4:"line";i:51;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:23;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:101;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9927584;}i:77;a:6:{i:0;s:20:"Checking role: sales";i:1;i:8;i:2;s:40:"yii\rbac\DbManager::checkAccessRecursive";i:3;d:1748933240.365741;i:4;a:3:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\menu.php";s:4:"line";i:51;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:23;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:101;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9928888;}i:78;a:6:{i:0;s:60:"SELECT "parent" FROM "auth_item_child" WHERE "child"='sales'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.365907;i:4;a:3:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\menu.php";s:4:"line";i:51;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:23;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:101;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9936008;}i:81;a:6:{i:0;s:78:"Rendering view file: D:\OSPanel\domains\silverzavod\views\layouts\sub-menu.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1748933240.371515;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:43;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:101;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10016032;}i:82;a:6:{i:0;s:75:"Rendering view file: D:\OSPanel\domains\silverzavod\views\layouts\modal.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:1748933240.372421;i:4;a:2:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:56;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:101;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:10046088;}}}";s:9:"profiling";s:21390:"a:3:{s:6:"memory";i:10281360;s:4:"time";d:0.23960208892822266;s:8:"messages";a:26:{i:7;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1748933240.201977;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6033088;}i:8;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1748933240.247211;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6035016;}i:10;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.248539;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6170952;}i:11;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.281818;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6186312;}i:13;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.283021;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6281184;}i:14;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.286247;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6282744;}i:16;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.289821;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6575808;}i:17;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.293491;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6578816;}i:49;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.312776;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8722128;}i:50;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.316212;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8724288;}i:52;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.316785;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8728744;}i:53;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.319569;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8730840;}i:57;a:6:{i:0;s:462:"
            SELECT
                mp.material_id,
                m.name as material_name,
                SUM(mp.quantity) as quantity,
                MAX(mp.created_at) as created_at
            FROM material_production mp
            LEFT JOIN material m ON m.id = mp.material_id
            WHERE mp.deleted_at IS NULL
            GROUP BY mp.material_id, m.name
            HAVING SUM(mp.quantity) > 0
            ORDER BY m.name ASC
        ";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.320187;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:43;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8734192;}i:58;a:6:{i:0;s:462:"
            SELECT
                mp.material_id,
                m.name as material_name,
                SUM(mp.quantity) as quantity,
                MAX(mp.created_at) as created_at
            FROM material_production mp
            LEFT JOIN material m ON m.id = mp.material_id
            WHERE mp.deleted_at IS NULL
            GROUP BY mp.material_id, m.name
            HAVING SUM(mp.quantity) > 0
            ORDER BY m.name ASC
        ";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.324951;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:43;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8737944;}i:60;a:6:{i:0;s:459:"
            SELECT
                ms.material_id,
                m.name as material_name,
                SUM(ms.quantity) as quantity,
                MAX(ms.created_at) as created_at
            FROM material_storage ms
            LEFT JOIN material m ON m.id = ms.material_id
            WHERE ms.deleted_at IS NULL
            GROUP BY ms.material_id, m.name
            HAVING SUM(ms.quantity) > 0
            ORDER BY m.name ASC
        ";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.325469;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:59;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8740088;}i:61;a:6:{i:0;s:459:"
            SELECT
                ms.material_id,
                m.name as material_name,
                SUM(ms.quantity) as quantity,
                MAX(ms.created_at) as created_at
            FROM material_storage ms
            LEFT JOIN material m ON m.id = ms.material_id
            WHERE ms.deleted_at IS NULL
            GROUP BY ms.material_id, m.name
            HAVING SUM(ms.quantity) > 0
            ORDER BY m.name ASC
        ";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.328134;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:59;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8744368;}i:63;a:6:{i:0;s:613:"
            SELECT
                ps.product_id,
                p.name as product_name,
                SUM(ps.quantity) as quantity,
                SUM(ps.quantity_block) as quantity_block,
                MAX(ps.enter_date) as enter_date
            FROM product_storage ps
            LEFT JOIN product p ON p.id = ps.product_id
            WHERE ps.deleted_at IS NULL
                AND ps.accepted_at IS NOT NULL
                AND ps.accepted_user_id IS NOT NULL
            GROUP BY ps.product_id, p.name
            HAVING SUM(ps.quantity) > 0
            ORDER BY p.name ASC
        ";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.328469;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:78;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8746512;}i:64;a:6:{i:0;s:613:"
            SELECT
                ps.product_id,
                p.name as product_name,
                SUM(ps.quantity) as quantity,
                SUM(ps.quantity_block) as quantity_block,
                MAX(ps.enter_date) as enter_date
            FROM product_storage ps
            LEFT JOIN product p ON p.id = ps.product_id
            WHERE ps.deleted_at IS NULL
                AND ps.accepted_at IS NOT NULL
                AND ps.accepted_user_id IS NOT NULL
            GROUP BY ps.product_id, p.name
            HAVING SUM(ps.quantity) > 0
            ORDER BY p.name ASC
        ";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.333249;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:78;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8748048;}i:66;a:6:{i:0;s:78:"SELECT "id", "name" FROM "material" WHERE "deleted_at" IS NULL ORDER BY "name"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.334207;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:86;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8792840;}i:67;a:6:{i:0;s:78:"SELECT "id", "name" FROM "material" WHERE "deleted_at" IS NULL ORDER BY "name"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.335079;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:86;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8797376;}i:69;a:6:{i:0;s:85:"SELECT "id", "name", "size" FROM "product" WHERE "deleted_at" IS NULL ORDER BY "name"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.335885;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:94;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8825432;}i:70;a:6:{i:0;s:85:"SELECT "id", "name", "size" FROM "product" WHERE "deleted_at" IS NULL ORDER BY "name"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.33668;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:94;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8832816;}i:75;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='sales'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.364252;i:4;a:3:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\menu.php";s:4:"line";i:51;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:23;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:101;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9930944;}i:76;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='sales'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.365367;i:4;a:3:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\menu.php";s:4:"line";i:51;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:23;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:101;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9933784;}i:79;a:6:{i:0;s:60:"SELECT "parent" FROM "auth_item_child" WHERE "child"='sales'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.365937;i:4;a:3:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\menu.php";s:4:"line";i:51;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:23;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:101;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9939384;}i:80;a:6:{i:0;s:60:"SELECT "parent" FROM "auth_item_child" WHERE "child"='sales'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.368192;i:4;a:3:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\menu.php";s:4:"line";i:51;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:23;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:101;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9941424;}}}";s:2:"db";s:20569:"a:1:{s:8:"messages";a:24:{i:10;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.248539;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6170952;}i:11;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.281818;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6186312;}i:13;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.283021;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6281184;}i:14;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.286247;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6282744;}i:16;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.289821;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6575808;}i:17;a:6:{i:0;s:63:"SELECT * FROM "users" WHERE ("id"=1) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.293491;i:4;a:1:{i:0;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\silverzavod\modules\backend\models\Users.php";s:4:"line";i:108;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6578816;}i:49;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.312776;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8722128;}i:50;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='1'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.316212;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8724288;}i:52;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.316785;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8728744;}i:53;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='admin'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.319569;i:4;a:1:{i:0;a:5:{s:4:"file";s:77:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\BaseController.php";s:4:"line";i:40;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8730840;}i:57;a:6:{i:0;s:462:"
            SELECT
                mp.material_id,
                m.name as material_name,
                SUM(mp.quantity) as quantity,
                MAX(mp.created_at) as created_at
            FROM material_production mp
            LEFT JOIN material m ON m.id = mp.material_id
            WHERE mp.deleted_at IS NULL
            GROUP BY mp.material_id, m.name
            HAVING SUM(mp.quantity) > 0
            ORDER BY m.name ASC
        ";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.320187;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:43;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8734192;}i:58;a:6:{i:0;s:462:"
            SELECT
                mp.material_id,
                m.name as material_name,
                SUM(mp.quantity) as quantity,
                MAX(mp.created_at) as created_at
            FROM material_production mp
            LEFT JOIN material m ON m.id = mp.material_id
            WHERE mp.deleted_at IS NULL
            GROUP BY mp.material_id, m.name
            HAVING SUM(mp.quantity) > 0
            ORDER BY m.name ASC
        ";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.324951;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:43;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8737944;}i:60;a:6:{i:0;s:459:"
            SELECT
                ms.material_id,
                m.name as material_name,
                SUM(ms.quantity) as quantity,
                MAX(ms.created_at) as created_at
            FROM material_storage ms
            LEFT JOIN material m ON m.id = ms.material_id
            WHERE ms.deleted_at IS NULL
            GROUP BY ms.material_id, m.name
            HAVING SUM(ms.quantity) > 0
            ORDER BY m.name ASC
        ";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.325469;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:59;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8740088;}i:61;a:6:{i:0;s:459:"
            SELECT
                ms.material_id,
                m.name as material_name,
                SUM(ms.quantity) as quantity,
                MAX(ms.created_at) as created_at
            FROM material_storage ms
            LEFT JOIN material m ON m.id = ms.material_id
            WHERE ms.deleted_at IS NULL
            GROUP BY ms.material_id, m.name
            HAVING SUM(ms.quantity) > 0
            ORDER BY m.name ASC
        ";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.328134;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:59;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8744368;}i:63;a:6:{i:0;s:613:"
            SELECT
                ps.product_id,
                p.name as product_name,
                SUM(ps.quantity) as quantity,
                SUM(ps.quantity_block) as quantity_block,
                MAX(ps.enter_date) as enter_date
            FROM product_storage ps
            LEFT JOIN product p ON p.id = ps.product_id
            WHERE ps.deleted_at IS NULL
                AND ps.accepted_at IS NOT NULL
                AND ps.accepted_user_id IS NOT NULL
            GROUP BY ps.product_id, p.name
            HAVING SUM(ps.quantity) > 0
            ORDER BY p.name ASC
        ";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.328469;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:78;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8746512;}i:64;a:6:{i:0;s:613:"
            SELECT
                ps.product_id,
                p.name as product_name,
                SUM(ps.quantity) as quantity,
                SUM(ps.quantity_block) as quantity_block,
                MAX(ps.enter_date) as enter_date
            FROM product_storage ps
            LEFT JOIN product p ON p.id = ps.product_id
            WHERE ps.deleted_at IS NULL
                AND ps.accepted_at IS NOT NULL
                AND ps.accepted_user_id IS NOT NULL
            GROUP BY ps.product_id, p.name
            HAVING SUM(ps.quantity) > 0
            ORDER BY p.name ASC
        ";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.333249;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:78;s:8:"function";s:8:"queryAll";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}}i:5;i:8748048;}i:66;a:6:{i:0;s:78:"SELECT "id", "name" FROM "material" WHERE "deleted_at" IS NULL ORDER BY "name"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.334207;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:86;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8792840;}i:67;a:6:{i:0;s:78:"SELECT "id", "name" FROM "material" WHERE "deleted_at" IS NULL ORDER BY "name"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.335079;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:86;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8797376;}i:69;a:6:{i:0;s:85:"SELECT "id", "name", "size" FROM "product" WHERE "deleted_at" IS NULL ORDER BY "name"";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.335885;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:94;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8825432;}i:70;a:6:{i:0;s:85:"SELECT "id", "name", "size" FROM "product" WHERE "deleted_at" IS NULL ORDER BY "name"";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.33668;i:4;a:1:{i:0;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:94;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:8832816;}i:75;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='sales'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.364252;i:4;a:3:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\menu.php";s:4:"line";i:51;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:23;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:101;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9930944;}i:76;a:6:{i:0;s:46:"SELECT * FROM "auth_item" WHERE "name"='sales'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.365367;i:4;a:3:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\menu.php";s:4:"line";i:51;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:23;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:101;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9933784;}i:79;a:6:{i:0;s:60:"SELECT "parent" FROM "auth_item_child" WHERE "child"='sales'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.365937;i:4;a:3:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\menu.php";s:4:"line";i:51;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:23;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:101;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9939384;}i:80;a:6:{i:0;s:60:"SELECT "parent" FROM "auth_item_child" WHERE "child"='sales'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748933240.368192;i:4;a:3:{i:0;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\menu.php";s:4:"line";i:51;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:53:"D:\OSPanel\domains\silverzavod\views\layouts\main.php";s:4:"line";i:23;s:8:"function";s:6:"render";s:5:"class";s:13:"yii\base\View";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:92:"D:\OSPanel\domains\silverzavod\modules\backend\controllers\InventoryAdjustmentController.php";s:4:"line";i:101;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:9941424;}}}";s:5:"event";s:9303:"a:51:{i:0;a:5:{s:4:"time";d:1748933240.196497;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:1748933240.247199;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:1748933240.293983;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:3;a:5:{s:4:"time";d:1748933240.294034;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:4;a:5:{s:4:"time";d:1748933240.29924;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\modules\backend\models\Users";}i:5;a:5:{s:4:"time";d:1748933240.306809;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:1748933240.309136;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:7;a:5:{s:4:"time";d:1748933240.309148;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\modules\backend\BackendModule";}i:8;a:5:{s:4:"time";d:1748933240.311074;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:9;a:5:{s:4:"time";d:1748933240.311093;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:10;a:5:{s:4:"time";d:1748933240.311102;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:11;a:5:{s:4:"time";d:1748933240.311108;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:12;a:5:{s:4:"time";d:1748933240.311113;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:13;a:5:{s:4:"time";d:1748933240.311118;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:14;a:5:{s:4:"time";d:1748933240.311123;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:15;a:5:{s:4:"time";d:1748933240.311156;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:61:"app\modules\backend\controllers\InventoryAdjustmentController";}i:16;a:5:{s:4:"time";d:1748933240.334063;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:17;a:5:{s:4:"time";d:1748933240.33576;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:18;a:5:{s:4:"time";d:1748933240.338919;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:19;a:5:{s:4:"time";d:1748933240.346998;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:20;a:5:{s:4:"time";d:1748933240.347437;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:21;a:5:{s:4:"time";d:1748933240.354444;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:22;a:5:{s:4:"time";d:1748933240.360229;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:23;a:5:{s:4:"time";d:1748933240.360354;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:24;a:5:{s:4:"time";d:1748933240.360537;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:25;a:5:{s:4:"time";d:1748933240.360839;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:26;a:5:{s:4:"time";d:1748933240.360902;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:27;a:5:{s:4:"time";d:1748933240.36103;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\widgets\ActiveForm";}i:28;a:5:{s:4:"time";d:1748933240.36111;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:29;a:5:{s:4:"time";d:1748933240.361516;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:30;a:5:{s:4:"time";d:1748933240.36236;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:31;a:5:{s:4:"time";d:1748933240.362903;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:32;a:5:{s:4:"time";d:1748933240.363085;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:33;a:5:{s:4:"time";d:1748933240.371259;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:34;a:5:{s:4:"time";d:1748933240.371507;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:35;a:5:{s:4:"time";d:1748933240.37214;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:36;a:5:{s:4:"time";d:1748933240.372412;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:37;a:5:{s:4:"time";d:1748933240.372964;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:38;a:5:{s:4:"time";d:1748933240.372988;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:39;a:5:{s:4:"time";d:1748933240.372998;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:40;a:5:{s:4:"time";d:1748933240.373054;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:41;a:5:{s:4:"time";d:1748933240.373907;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:42;a:5:{s:4:"time";d:1748933240.374404;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:43;a:5:{s:4:"time";d:1748933240.374812;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:44;a:5:{s:4:"time";d:1748933240.374862;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:61:"app\modules\backend\controllers\InventoryAdjustmentController";}i:45;a:5:{s:4:"time";d:1748933240.374875;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"app\modules\backend\BackendModule";}i:46;a:5:{s:4:"time";d:1748933240.374883;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:47;a:5:{s:4:"time";d:1748933240.374896;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:48;a:5:{s:4:"time";d:1748933240.374906;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:49;a:5:{s:4:"time";d:1748933240.37561;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:50;a:5:{s:4:"time";d:1748933240.37605;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:92:"a:3:{s:5:"start";d:1748933240.137748;s:3:"end";d:1748933240.378052;s:6:"memory";i:10281360;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:4884:"a:3:{s:8:"messages";a:24:{i:21;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.30716;i:4;a:0:{}i:5;i:8100456;}i:22;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307171;i:4;a:0:{}i:5;i:8101208;}i:23;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307256;i:4;a:0:{}i:5;i:8101960;}i:24;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.30726;i:4;a:0:{}i:5;i:8102712;}i:25;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307264;i:4;a:0:{}i:5;i:8103464;}i:26;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307268;i:4;a:0:{}i:5;i:8104216;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:5:"login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307272;i:4;a:0:{}i:5;i:8104968;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:6:"logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307275;i:4;a:0:{}i:5;i:8105720;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:13:"site/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307279;i:4;a:0:{}i:5;i:8106472;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:39:"api/<controller:[\w-]+>/<action:[\w-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307283;i:4;a:0:{}i:5;i:8107224;}i:31;a:6:{i:0;a:3:{s:4:"rule";s:23:"api/<controller:[\w-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307287;i:4;a:0:{}i:5;i:8107976;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:12:"swagger/json";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.30729;i:4;a:0:{}i:5;i:8108728;}i:33;a:6:{i:0;a:3:{s:4:"rule";s:10:"swagger/ui";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307295;i:4;a:0:{}i:5;i:8110760;}i:34;a:6:{i:0;a:3:{s:4:"rule";s:7:"swagger";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307298;i:4;a:0:{}i:5;i:8111512;}i:35;a:6:{i:0;a:3:{s:4:"rule";s:25:"backend/position/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307302;i:4;a:0:{}i:5;i:8112264;}i:36;a:6:{i:0;a:3:{s:4:"rule";s:16:"backend/position";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307305;i:4;a:0:{}i:5;i:8113016;}i:37;a:6:{i:0;a:3:{s:4:"rule";s:32:"backend/equipment/<id:\d+>/parts";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307309;i:4;a:0:{}i:5;i:8113768;}i:38;a:6:{i:0;a:3:{s:4:"rule";s:60:"backend/equipment/defect-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307415;i:4;a:0:{}i:5;i:8114520;}i:39;a:6:{i:0;a:3:{s:4:"rule";s:61:"backend/equipment/reserve-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307419;i:4;a:0:{}i:5;i:8115272;}i:40;a:6:{i:0;a:3:{s:4:"rule";s:60:"backend/equipment/repair-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307423;i:4;a:0:{}i:5;i:8116024;}i:41;a:6:{i:0;a:3:{s:4:"rule";s:62:"backend/equipment/activate-part/<equipmentId:\d+>/<partId:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307428;i:4;a:0:{}i:5;i:8116776;}i:42;a:6:{i:0;a:3:{s:4:"rule";s:38:"backend/<controller>/<action>/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307432;i:4;a:0:{}i:5;i:8117528;}i:43;a:6:{i:0;s:59:"Request parsed with URL rule: backend/<controller>/<action>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:1748933240.30745;i:4;a:0:{}i:5;i:8119616;}i:44;a:6:{i:0;a:3:{s:4:"rule";s:29:"backend/<controller>/<action>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1748933240.307455;i:4;a:0:{}i:5;i:8119440;}}s:5:"route";s:34:"backend/inventory-adjustment/index";s:6:"action";s:76:"app\modules\backend\controllers\InventoryAdjustmentController::actionIndex()";}";s:7:"request";s:5767:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:10:{s:4:"host";s:6:"silver";s:10:"connection";s:10:"keep-alive";s:13:"cache-control";s:9:"max-age=0";s:25:"upgrade-insecure-requests";s:1:"1";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:7:"referer";s:36:"http://silver/backend/position/index";s:15:"accept-encoding";s:13:"gzip, deflate";s:15:"accept-language";s:65:"en-RU,en;q=0.9,ru-RU;q=0.8,ru;q=0.7,en-US;q=0.6,uz;q=0.5,hr;q=0.4";s:6:"cookie";s:539:"language=4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a%3A2%3A%7Bi%3A0%3Bs%3A8%3A%22language%22%3Bi%3A1%3Bs%3A2%3A%22uz%22%3B%7D; PHPSESSID=8qss1del29954ht3d4l8a7jcpm0slk0c; _identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; _csrf=b4d06607184bb4d9ae4ccfcbbf381e70b71aea7f8466ee73aa71aa11929d742aa%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22ypYzj4sA9wySdTWdkUjWGW9WbsZ8gFZ7%22%3B%7D";}s:15:"responseHeaders";a:8:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"683e9a7849ee4";s:16:"X-Debug-Duration";s:3:"239";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=683e9a7849ee4";s:10:"Set-Cookie";s:260:"_identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; expires=Thu, 03-Jul-2025 06:47:20 GMT; Max-Age=2592000; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:34:"backend/inventory-adjustment/index";s:6:"action";s:76:"app\modules\backend\controllers\InventoryAdjustmentController::actionIndex()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:39:{s:15:"REDIRECT_STATUS";s:3:"200";s:9:"HTTP_HOST";s:6:"silver";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:18:"HTTP_CACHE_CONTROL";s:9:"max-age=0";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:12:"HTTP_REFERER";s:36:"http://silver/backend/position/index";s:20:"HTTP_ACCEPT_ENCODING";s:13:"gzip, deflate";s:20:"HTTP_ACCEPT_LANGUAGE";s:65:"en-RU,en;q=0.9,ru-RU;q=0.8,ru;q=0.7,en-US;q=0.6,uz;q=0.5,hr;q=0.4";s:11:"HTTP_COOKIE";s:539:"language=4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a%3A2%3A%7Bi%3A0%3Bs%3A8%3A%22language%22%3Bi%3A1%3Bs%3A2%3A%22uz%22%3B%7D; PHPSESSID=8qss1del29954ht3d4l8a7jcpm0slk0c; _identity=d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa%3A2%3A%7Bi%3A0%3Bs%3A9%3A%22_identity%22%3Bi%3A1%3Bs%3A16%3A%22%5B1%2Cnull%2C2592000%5D%22%3B%7D; _csrf=b4d06607184bb4d9ae4ccfcbbf381e70b71aea7f8466ee73aa71aa11929d742aa%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22ypYzj4sA9wySdTWdkUjWGW9WbsZ8gFZ7%22%3B%7D";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:6:"silver";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:44:"D:/OSPanel/domains/silverzavod/web/index.php";s:11:"REMOTE_PORT";s:5:"64104";s:12:"REDIRECT_URL";s:35:"/backend/inventory-adjustment/index";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:3:"GET";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:35:"/backend/inventory-adjustment/index";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1748933240.125389;s:12:"REQUEST_TIME";i:1748933240;}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:4:{s:8:"language";s:102:"4e1a22a706b2b67a2ac68d2d85cb839925d09aed085ca214408c5498b87a9ea2a:2:{i:0;s:8:"language";i:1;s:2:"uz";}";s:9:"PHPSESSID";s:32:"8qss1del29954ht3d4l8a7jcpm0slk0c";s:9:"_identity";s:118:"d3bbf1a40fbb07a5e71f4b657b0d9caaa7c20725eb7f86d2213424408f95f8caa:2:{i:0;s:9:"_identity";i:1;s:16:"[1,null,2592000]";}";s:5:"_csrf";s:130:"b4d06607184bb4d9ae4ccfcbbf381e70b71aea7f8466ee73aa71aa11929d742aa:2:{i:0;s:5:"_csrf";i:1;s:32:"ypYzj4sA9wySdTWdkUjWGW9WbsZ8gFZ7";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:6:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";N;s:8:"__expire";i:1748940440;s:13:"last_activity";i:1748933240;s:7:"timeout";i:1800;}}";s:4:"user";s:2148:"a:5:{s:2:"id";i:1;s:8:"identity";a:8:{s:2:"id";s:1:"1";s:8:"username";s:7:"'admin'";s:9:"full_name";s:15:"'Administrator'";s:4:"role";s:1:"1";s:12:"access_token";s:42:"'hDlF38UoPMOH4Koq5kFA5mtUZQW1FIcC2x1ZShA3'";s:8:"password";s:62:"'$2y$13$4TIVTzSuUrDvbyA/XTxgYeLOUkjr1YmfDSmKDjzI.OxKnUvJRzfk2'";s:10:"created_at";s:21:"'2025-02-24 16:46:43'";s:10:"deleted_at";s:4:"null";}s:10:"attributes";a:8:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"ID";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:10:"Логин";}i:2;a:2:{s:9:"attribute";s:9:"full_name";s:5:"label";s:6:"ФИО";}i:3;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:4;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}i:5;a:2:{s:9:"attribute";s:8:"password";s:5:"label";s:12:"Пароль";}i:6;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:25:"Дата создания";}i:7;a:2:{s:9:"attribute";s:10:"deleted_at";s:5:"label";s:25:"Дата удаления";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";N;s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:1:{s:5:"admin";a:7:{s:4:"type";i:1;s:4:"name";s:5:"admin";s:11:"description";s:13:"Administrator";s:8:"ruleName";N;s:4:"data";s:4:"null";s:9:"createdAt";i:**********;s:9:"updatedAt";i:**********;}}s:10:"modelClass";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-1";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}}";s:5:"asset";s:2881:"a:4:{s:27:"yii\widgets\ActiveFormAsset";a:9:{s:10:"sourcePath";s:57:"D:\OSPanel\domains\silverzavod\vendor\yiisoft\yii2/assets";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\e0686f09";s:7:"baseUrl";s:16:"/assets/e0686f09";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:17:"yii.activeForm.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\web\YiiAsset";a:9:{s:10:"sourcePath";s:57:"D:\OSPanel\domains\silverzavod\vendor\yiisoft\yii2/assets";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\e0686f09";s:7:"baseUrl";s:16:"/assets/e0686f09";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:6:"yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:61:"D:\OSPanel\domains\silverzavod\vendor/bower-asset/jquery/dist";s:8:"basePath";s:50:"D:\OSPanel\domains\silverzavod\web\assets\e0ff1908";s:7:"baseUrl";s:16:"/assets/e0ff1908";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"app\assets\AppAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:34:"D:/OSPanel/domains/silverzavod/web";s:7:"baseUrl";s:0:"";s:7:"depends";a:2:{i:0;s:16:"yii\web\YiiAsset";i:1;s:19:"yii\web\JqueryAsset";}s:2:"js";a:18:{i:0;s:37:"modules/bootstrap/js/bootstrap.min.js";i:1;s:35:"modules/bootstrap-datepicker.min.js";i:2;s:43:"modules/nicescroll/jquery.nicescroll.min.js";i:3;s:21:"modules/moment.min.js";i:4;s:52:"modules/bootstrap-daterangepicker/daterangepicker.js";i:5;s:12:"js/stisla.js";i:6;s:35:"modules/izitoast/js/iziToast.min.js";i:7;s:38:"modules/select2/dist/js/select2.min.js";i:8;s:34:"modules/jquery-ui/jquery-ui.min.js";i:9;s:13:"js/scripts.js";i:10;s:26:"js/jquery.inputmask.min.js";i:11;s:47:"modules/chocolat/dist/js/jquery.chocolat.min.js";i:12;s:27:"js/jquery.magnific-popup.js";i:13;s:10:"js/menu.js";i:14;s:21:"modules/moment.min.js";i:15;s:59:"modules/bootstrap-timepicker/js/bootstrap-timepicker.min.js";i:16;s:64:"//cdnjs.cloudflare.com/ajax/libs/numeral.js/2.0.6/numeral.min.js";i:17;s:21:"js/input-formatter.js";}s:3:"css";a:12:{i:0;s:12:"css/site.css";i:1;s:39:"modules/bootstrap/css/bootstrap.min.css";i:2;s:53:"modules/bootstrap-daterangepicker/daterangepicker.css";i:3;s:36:"modules/bootstrap-datepicker.min.css";i:4;s:35:"modules/fontawesome/css/all.min.css";i:5;s:37:"modules/ionicons/css/ionicons.min.css";i:6;s:40:"modules/select2/dist/css/select2.min.css";i:7;s:37:"modules/izitoast/css/iziToast.min.css";i:8;s:13:"css/style.css";i:9;s:18:"css/components.css";i:10;s:22:"css/magnific-popup.css";i:11;s:61:"modules/bootstrap-timepicker/css/bootstrap-timepicker.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}}";s:7:"summary";a:13:{s:3:"tag";s:13:"683e9a7849ee4";s:3:"url";s:48:"http://silver/backend/inventory-adjustment/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1748933240.125389;s:10:"statusCode";i:200;s:8:"sqlCount";i:12;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10281360;s:14:"processingTime";d:0.23960208892822266;}s:10:"exceptions";a:0:{}}